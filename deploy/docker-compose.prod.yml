version: '3.8'

services:
  dh-index-backend:
    image: dh-index-backend:latest
    ports:
      - "8000:8000"
    volumes:
      # Mount data directory để bảo toàn database và logs
      - /opt/dh-index-backend/data:/app/data
      - /opt/dh-index-backend/logs:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=dh_index.settings
      - DATABASE_PATH=/app/data/db.sqlite3
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
