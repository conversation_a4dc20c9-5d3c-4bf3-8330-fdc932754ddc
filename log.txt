Running tests...
+ echo Starting test container...
Starting test container...
+ docker run -d --name test-backend-7 -p 8001:8000 dh-index-backend:7
3b15c734e11f9ea7b48e32b321fe064dd999d69f944d8fc6a9c33969d71057fe
+ echo Waiting for container to start...
Waiting for container to start...
+ sleep 5
+ echo Checking container status immediately...
Checking container status immediately...
+ docker ps -a
+ grep test-backend-7
3b15c734e11f   dh-index-backend:7         "bash /app/start.sh"     5 seconds ago   Exited (1) 3 seconds ago             test-backend-7
+ echo Getting container logs...
Getting container logs...
+ docker logs test-backend-7
=== Starting DH Index Backend ===
Checking Python and Django...
Python 3.10.18
Django version: 5.2.4
Chạy migrations...
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 279, in ensure_connection
    self.connect()
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 256, in connect
    self.connection = self.get_new_connection(conn_params)
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/sqlite3/base.py", line 206, in get_new_connection
    conn = Database.connect(**conn_params)
sqlite3.OperationalError: unable to open database file

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/manage.py", line 22, in <module>
    main()
  File "/app/manage.py", line 18, in main
    execute_from_command_line(sys.argv)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/__init__.py", line 442, in execute_from_command_line
    utility.execute()
  File "/usr/local/lib/python3.10/site-packages/django/core/management/__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/base.py", line 416, in run_from_argv
    self.execute(*args, **cmd_options)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/base.py", line 457, in execute
    self.check(**check_kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/base.py", line 492, in check
    all_issues = checks.run_checks(
  File "/usr/local/lib/python3.10/site-packages/django/core/checks/registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
  File "/usr/local/lib/python3.10/site-packages/django/core/checks/model_checks.py", line 36, in check_all_models
    errors.extend(model.check(**kwargs))
  File "/usr/local/lib/python3.10/site-packages/django/db/models/base.py", line 1719, in check
    *cls._check_fields(**kwargs),
  File "/usr/local/lib/python3.10/site-packages/django/db/models/base.py", line 1914, in _check_fields
    errors.extend(field.check(**kwargs))
  File "/usr/local/lib/python3.10/site-packages/django/db/models/fields/json.py", line 49, in check
    errors.extend(self._check_supported(databases))
  File "/usr/local/lib/python3.10/site-packages/django/db/models/fields/json.py", line 65, in _check_supported
    or connection.features.supports_json_field
  File "/usr/local/lib/python3.10/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/sqlite3/features.py", line 144, in supports_json_field
    with self.connection.cursor() as cursor:
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 320, in cursor
    return self._cursor()
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 296, in _cursor
    self.ensure_connection()
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 278, in ensure_connection
    with self.wrap_database_errors:
  File "/usr/local/lib/python3.10/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 279, in ensure_connection
    self.connect()
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 256, in connect
    self.connection = self.get_new_connection(conn_params)
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/sqlite3/base.py", line 206, in get_new_connection
    conn = Database.connect(**conn_params)
django.db.utils.OperationalError: unable to open database file
+ echo Waiting a bit more...
Waiting a bit more...
+ sleep 10
+ echo Checking if container is still running...
Checking if container is still running...
+ docker ps
+ grep -q test-backend-7
+ echo Container stopped unexpectedly!
Container stopped unexpectedly!
+ echo Final container status:
Final container status:
+ docker ps -a
+ grep test-backend-7
3b15c734e11f   dh-index-backend:7         "bash /app/start.sh"     15 seconds ago   Exited (1) 13 seconds ago             test-backend-7
+ echo Final container logs:
Final container logs:
+ docker logs test-backend-7
=== Starting DH Index Backend ===
Checking Python and Django...
Python 3.10.18
Django version: 5.2.4
Chạy migrations...
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 279, in ensure_connection
    self.connect()
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 256, in connect
    self.connection = self.get_new_connection(conn_params)
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/sqlite3/base.py", line 206, in get_new_connection
    conn = Database.connect(**conn_params)
sqlite3.OperationalError: unable to open database file

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/manage.py", line 22, in <module>
    main()
  File "/app/manage.py", line 18, in main
    execute_from_command_line(sys.argv)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/__init__.py", line 442, in execute_from_command_line
    utility.execute()
  File "/usr/local/lib/python3.10/site-packages/django/core/management/__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/base.py", line 416, in run_from_argv
    self.execute(*args, **cmd_options)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/base.py", line 457, in execute
    self.check(**check_kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/core/management/base.py", line 492, in check
    all_issues = checks.run_checks(
  File "/usr/local/lib/python3.10/site-packages/django/core/checks/registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
  File "/usr/local/lib/python3.10/site-packages/django/core/checks/model_checks.py", line 36, in check_all_models
    errors.extend(model.check(**kwargs))
  File "/usr/local/lib/python3.10/site-packages/django/db/models/base.py", line 1719, in check
    *cls._check_fields(**kwargs),
  File "/usr/local/lib/python3.10/site-packages/django/db/models/base.py", line 1914, in _check_fields
    errors.extend(field.check(**kwargs))
  File "/usr/local/lib/python3.10/site-packages/django/db/models/fields/json.py", line 49, in check
    errors.extend(self._check_supported(databases))
  File "/usr/local/lib/python3.10/site-packages/django/db/models/fields/json.py", line 65, in _check_supported
    or connection.features.supports_json_field
  File "/usr/local/lib/python3.10/site-packages/django/utils/functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/sqlite3/features.py", line 144, in supports_json_field
    with self.connection.cursor() as cursor:
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 320, in cursor
    return self._cursor()
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 296, in _cursor
    self.ensure_connection()
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 278, in ensure_connection
    with self.wrap_database_errors:
  File "/usr/local/lib/python3.10/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 279, in ensure_connection
    self.connect()
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/base/base.py", line 256, in connect
    self.connection = self.get_new_connection(conn_params)
  File "/usr/local/lib/python3.10/site-packages/django/utils/asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/db/backends/sqlite3/base.py", line 206, in get_new_connection
    conn = Database.connect(**conn_params)
django.db.utils.OperationalError: unable to open database file
+ echo Container inspect:
Container inspect:
+ docker inspect test-backend-7 --format={{.State.ExitCode}} {{.State.Error}}
1 
+ docker rm test-backend-7
test-backend-7
+ exit 1
script returned exit code 1Pipeline completed!
[WS-CLEANUP] Deleting project workspace...
[WS-CLEANUP] Deferred wipeout is used...
[WS-CLEANUP] done
Pipeline failed!
